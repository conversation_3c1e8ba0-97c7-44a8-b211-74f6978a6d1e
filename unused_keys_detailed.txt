============================================================
I18N USAGE REPORT
============================================================
Total defined keys: 246
Total used keys: 233
Unused keys: 90
Undefined but used keys: 77

UNUSED KEYS:
----------------------------------------

  CHAT:
    chat.processing-exception

  CHUNK:
    chunk.load-callback
    chunk.unload-callback

  COMPONENT:
    component.bridge

  ENTITY:
    entity.target

  ERROR:
    error.class-not-found
    error.database-error
    error.field-not-found
    error.file-not-found
    error.invalid-configuration
    error.method-not-found
    error.mixin-error
    error.network-error
    error.permission-denied
    error.plugin-error

  EVENT:
    event.handler

  ITEM:
    item.legacy-itemstack

  LOOT:
    loot.container

  NETWORK:
    network.custom-payload
    network.packet

  OPTIMIZATION:
    optimization.async-ai
    optimization.async-collision
    optimization.async-event
    optimization.async-redstone
    optimization.chunk
    optimization.entity-cleaner
    optimization.entity-cleanup
    optimization.manager
    optimization.memory
    optimization.thread
    optimization.thread-pool

  PLAYER:
    player.data

  RECIPE:
    recipe.loading

  REGISTRY:
    registry.block-state
    registry.block-state.not-subclass
    registry.entity
    registry.entity.not-subclass
    registry.meta-type
    registry.meta-type.not-subclass

  RELEASE-NAME:
    release-name.Executions
    release-name.GreatHorn
    release-name.Horn
    release-name.Trials

  ROOT:
    auth
    bootstrap
    chat
    chunk
    classloader
    component
    dist
    entity
    enum
    error
    event
    i18n
    item
    java
    loot
    material
    mixin-load
    mod
    network
    optimization
    patcher
    permission
    player
    recipe
    registry
    release-name
    server
    sign
    symlink
    velocity
    warning
    world

  SERVER:
    server.async-world-save
    server.threads
    server.world-save

  VELOCITY:
    velocity.address
    velocity.forwarding
    velocity.login
    velocity.packet
    velocity.query
    velocity.signature

  WARNING:
    warning.async-operation
    warning.deprecated-api
    warning.disk-space-low
    warning.memory-low
    warning.performance-issue
    warning.plugin-conflict

UNDEFINED BUT USED KEYS:
----------------------------------------
  Bukkit.MaxHealth
  BukkitValues
  ChunkBukkitValues
  dimension
  entries
  enum.not-found-warning
  forgeCaps
  getReturnValue%s
  release-name.
  sign.non-editable-warning
  symlink.create-error

Note: Filtered out 66 likely false positives

RECOMMENDATIONS:
----------------------------------------
📝 Consider removing unused i18n keys to reduce file size:
  en_us.conf: 90 unused keys
🔧 Add missing i18n keys to translation files:
  Bukkit.MaxHealth
  BukkitValues
  ChunkBukkitValues
  dimension
  entries
  enum.not-found-warning
  forgeCaps
  getReturnValue%s
  release-name.
  sign.non-editable-warning
  symlink.create-error

SUMMARY:
----------------------------------------
⚠️  90 keys are defined but not used
❌ 11 keys are used but not defined (after filtering)