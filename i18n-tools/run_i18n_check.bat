@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    Luminara I18n Usage Checker
echo ========================================
echo.

:menu
echo 请选择要执行的操作:
echo.
echo 1. 快速检查 (推荐日常使用)
echo 2. 完整分析报告
echo 3. 生成清理建议
echo 4. 检查非i18n logger调用
echo 5. 保守清理i18n文件
echo 6. 查看所有报告文件
echo 7. 退出
echo.
set /p choice=请输入选项 (1-7):

if "%choice%"=="1" goto quick_check
if "%choice%"=="2" goto full_report
if "%choice%"=="3" goto cleanup_suggestions
if "%choice%"=="4" goto non_i18n_check
if "%choice%"=="5" goto conservative_clean
if "%choice%"=="6" goto view_reports
if "%choice%"=="7" goto exit
echo 无效选项，请重新选择
goto menu

:quick_check
echo.
echo 🔍 执行快速检查...
python quick_i18n_check.py
echo.
pause
goto menu

:full_report
echo.
echo 📊 生成完整分析报告...
python check_i18n_usage.py -o i18n_analysis_report.txt --detailed
echo ✅ 报告已保存到: i18n_analysis_report.txt
echo.
pause
goto menu

:cleanup_suggestions
echo.
echo 🧹 生成清理建议...
python i18n_cleanup_helper.py -o i18n_cleanup_detailed.txt
echo ✅ 清理建议已保存到: i18n_cleanup_detailed.txt
echo.
pause
goto menu

:non_i18n_check
echo.
echo 🔍 检查非i18n logger调用...
python non_i18n_logger_checker.py -o non_i18n_logger_report.txt
echo ✅ 报告已保存到: non_i18n_logger_report.txt
echo.
pause
goto menu

:conservative_clean
echo.
echo ⚠️  这将清理i18n文件，确保已备份！
set /p confirm=确认执行清理？(y/N):
if /i "%confirm%"=="y" (
    echo 🧹 执行保守清理...
    python conservative_i18n_cleaner.py --force
    echo ✅ 清理完成！
) else (
    echo 🧪 执行干运行预览...
    python conservative_i18n_cleaner.py --dry-run
)
echo.
pause
goto menu

:view_reports
echo.
echo 📄 当前目录中的报告文件:
echo.
if exist "i18n_analysis_report.txt" echo ✅ i18n_analysis_report.txt - 完整分析报告
if exist "i18n_cleanup_detailed.txt" echo ✅ i18n_cleanup_detailed.txt - 详细清理建议
if exist "non_i18n_logger_report.txt" echo ✅ non_i18n_logger_report.txt - 非i18n logger报告
if exist "i18n_system_summary.md" echo ✅ i18n_system_summary.md - 系统归整总结
if exist "README_i18n_tools.md" echo ✅ README_i18n_tools.md - 工具使用说明
echo.
set /p open_file=输入文件名打开 (或按回车返回菜单): 
if "%open_file%"=="" goto menu
if exist "%open_file%" (
    notepad "%open_file%"
) else (
    echo 文件不存在: %open_file%
)
echo.
pause
goto menu

:exit
echo.
echo 👋 感谢使用 Luminara I18n Usage Checker!
echo.
pause
exit
