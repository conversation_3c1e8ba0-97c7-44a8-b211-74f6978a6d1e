============================================================
I18N USAGE REPORT
============================================================
Total defined keys: 146
Total used keys: 87
Unused keys: 67
Undefined but used keys: 8

UNUSED KEYS:
----------------------------------------

  IMPLEMENTER:
    Implementer.error
    Implementer.not-found

  CHAT:
    chat.illegal-characters
    chat.message-too-long
    chat.player-removed

  DIST:
    dist.logic-world-check

  ERROR:
    error.class-not-found
    error.database-error
    error.field-not-found
    error.file-not-found
    error.invalid-configuration
    error.method-not-found
    error.mixin-error
    error.network-error
    error.permission-denied
    error.plugin-error

  I18N:
    i18n.current-not-available

  IMPLEMENTER:
    implementer.error
    implementer.not-found

  MOD:
    mod.conflict-detected

  OPTIMIZATION:
    optimization.async-ai
    optimization.async-collision
    optimization.async-event
    optimization.chunk
    optimization.entity-cleanup
    optimization.manager
    optimization.memory

  PLAYER:
    player.dropped-items-disconnect
    player.internal-command-error
    player.invalid-hotbar-disconnect

  REGISTRY:
    registry.block-state
    registry.block-state.not-subclass
    registry.entity
    registry.entity.not-subclass
    registry.meta-type
    registry.meta-type.not-subclass

  RELEASE-NAME:
    release-name.Executions
    release-name.GreatHorn
    release-name.Horn
    release-name.Trials

  ROOT:
    Implementer
    chat
    dist
    enum
    error
    i18n
    implementer
    java
    mixin-load
    mod
    optimization
    patcher
    player
    registry
    release-name
    server
    sign
    symlink
    velocity
    warning
    world

  WARNING:
    warning.async-operation
    warning.deprecated-api
    warning.disk-space-low
    warning.memory-low
    warning.performance-issue
    warning.plugin-conflict

UNDEFINED BUT USED KEYS:
----------------------------------------
  Bukkit.MaxHealth
  BukkitValues
  ChunkBukkitValues
  dimension
  entries
  forgeCaps
  getReturnValue%s
  release-name.

RECOMMENDATIONS:
----------------------------------------
📝 Consider removing unused i18n keys to reduce file size:
  en_us.conf: 64 unused keys
  ru_ru.conf: 3 unused keys
🔧 Add missing i18n keys to translation files:
  Bukkit.MaxHealth
  BukkitValues
  ChunkBukkitValues
  dimension
  entries
  forgeCaps
  getReturnValue%s
  release-name.

SUMMARY:
----------------------------------------
⚠️  67 keys are defined but not used
❌ 8 keys are used but not defined (after filtering)