NON-I18N LOGGER USAGE REPORT
==================================================

📊 SUMMARY:
   Files scanned: 40
   Hardcoded messages: 58
   Potential i18n keys: 93
   Technical strings: 0

🚨 HARDCODED MESSAGES (Should use i18n):
----------------------------------------

📄 main\java\io\izzel\arclight\common\mixin\core\network\ServerLoginNetHandlerMixin.java:
   Line 201: LOGGER.warn("Exception verifying {} ")
   Line 269: LOGGER.warn("Failed to verify username but will let them in anyway!")
   Line 274: LOGGER.error("Username '{}' tried to join with an invalid session")
   Line 278: LOGGER.warn("Authentication servers are down but will let them in anyway!")
   Line 283: LOGGER.error("Couldn't verify username because servers are unavailable")
   Line 330: LOGGER.info("UUID of player {} is {}")
   Line 356: LOGGER.warn("Received Velocity query response with null data")
   Line 364: LOGGER.info("Successfully processed Velocity forwarding for player: {}")
   Line 379: LOGGER.warn("Exception processing Velocity forwarding packet from {}")
   Line 398: LOGGER.warn("Exception verifying ")
   Line 416: LOGGER.info("UUID of player {} is {} (from Velocity)")
   Line 420: LOGGER.warn("Exception processing Velocity login for ")

📄 main\java\io\izzel\arclight\common\mixin\core\network\ServerPlayNetHandlerMixin.java:
   Line 1352: LOGGER.warn("Player {} tried to attack an invalid entity")
   Line 1827: LOGGER.error("Couldn't register custom payload")
   Line 1833: LOGGER.warn("Custom payload UNREGISTER too large: {} bytes")
   Line 1840: LOGGER.warn("Custom payload UNREGISTER string too long: {} characters")
   Line 1851: LOGGER.error("Couldn't unregister custom payload")
   Line 1858: LOGGER.error("Couldn't dispatch custom payload")

📄 main\java\io\izzel\arclight\common\mixin\core\network\protocol\PacketThreadUtilMixin.java:
   Line  44: LOGGER.error("Failed to handle packet {}, suppressing error")

📄 main\java\io\izzel\arclight\common\mixin\core\server\MinecraftServerMixin.java:
   Line 462: LOGGER.info("Preparing start region for dimension {}")
   Line 553: LOGGER.info("Preparing start region for dimension {}")
   Line 616: LOGGER.info("Starting async world save...")
   Line 625: LOGGER.error("Failed to save world {}")
   Line 643: LOGGER.info("All worlds saved successfully")
   Line 646: LOGGER.warn("Async world save did not complete within timeout or failed")

📄 main\java\io\izzel\arclight\common\mixin\core\server\dedicated\DedicatedServerMixin.java:
   Line 113: LOGGER.info("{} threads not shutting down correctly, force exiting")

📄 main\java\io\izzel\arclight\common\mixin\core\server\level\ChunkHolderMixin.java:
   Line  91: LOGGER.fatal("Failed to schedule unload callback for chunk ")
   Line 114: LOGGER.fatal("Failed to schedule load callback for chunk ")

📄 main\java\io\izzel\arclight\common\mixin\core\server\level\ServerLevelMixin.java:
   Line 205: LOGGER.warn("Assign {} to unknown level stem {}")

📄 main\java\io\izzel\arclight\common\mixin\core\world\entity\MobMixin.java:
   Line  76: LOGGER.warn("Unknown target reason setting {} target to {}")

📄 main\java\io\izzel\arclight\common\mixin\core\world\item\ItemStackMixin.java:
   Line  84: LOG.warn("Legacy ItemStack being used, updates will not applied: {}")

📄 main\java\io\izzel\arclight\common\mixin\core\world\item\crafting\RecipeManagerMixin.java:
   Line  77: LOGGER.info("Skipping loading recipe {} as it's serializer returned null")
   Line  84: LOGGER.error("Parsing error loading recipe {}")
   Line  90: LOGGER.info("Loaded {} recipes")

📄 main\java\io\izzel\arclight\common\mixin\core\world\level\storage\loot\LootTableMixin.java:
   Line  79: LOGGER.warn("Tried to over-fill a container")

📄 main\java\io\izzel\arclight\common\mixin\core\world\storage\PlayerDataMixin.java:
   Line  55: LOGGER.warn("Failed to load player data for ")

📄 main\java\io\izzel\arclight\common\mixin\forge\PermissionAPIMixin.java:
   Line  26: LOGGER.info("Forwarding forge permission[{}] to bukkit")

📄 main\java\io\izzel\arclight\common\mod\util\remapper\ClassLoaderRemapper.java:
   Line  89: LOGGER.error("Failed to dump class ")
   Line 192: LOGGER.warn("Loading CLIENT side class: {}")
   Line 376: LOGGER.warn("No class remap config is provided for class {}, using PLUGIN")
   Line 453: LOGGER.warn("Failed to find class {}")

📄 main\java\io\izzel\arclight\common\mod\velocity\VelocityForwarding.java:
   Line  41: LOGGER.info("Velocity Modern Forwarding initialized")
   Line  82: LOGGER.warn("Failed to set forwarded address via f_129469_, trying alternatives")
   Line  90: LOGGER.warn("Failed to set address via reflection, trying bridge method")
   Line  95: LOGGER.warn("Failed to set address via bridge")
   Line 112: LOGGER.info("Successfully processed Velocity forwarding for player: {} (online-mode: {})")
   Line 121: LOGGER.warn("Velocity packet too small: {} bytes")
   Line 141: LOGGER.warn("Velocity signature mismatch!")
   Line 151: LOGGER.error("Error verifying Velocity signature")

📄 main\java\io\izzel\arclight\common\optimization\mpem\AsyncEventSystem.java:
   Line  61: LOGGER.warn("Interrupted during shutdown")

📄 main\java\io\izzel\arclight\common\optimization\mpem\EntityCleaner.java:
   Line 395: LOGGER.info("Forcing entity cleanup...")
   Line 426: LOGGER.info("Scheduled entity cleanup cancelled")

📄 main\java\io\izzel\arclight\common\optimization\mpem\MpemThreadManager.java:
   Line  87: LOGGER.error("Uncaught exception in thread {}")

📄 main\java\io\izzel\arclight\common\optimization\mpem\async\AsyncAIManager.java:
   Line 143: LOGGER.warn("Error in AI calculations")

📄 main\java\io\izzel\arclight\common\optimization\mpem\async\AsyncCollisionSystem.java:
   Line 138: LOGGER.warn("Error handling async collision between {} and {}")
   Line 198: LOGGER.warn("Error in collision calculations")

📄 main\java\io\izzel\arclight\common\optimization\mpem\async\AsyncRedstoneManager.java:
   Line  55: LOGGER.warn("Error processing async redstone update at {}")
   Line  86: LOGGER.warn("Error calculating redstone signal")

🔍 POTENTIAL I18N KEYS (Already using keys):
----------------------------------------

📄 main\java\io\izzel\arclight\boot\mod\ModBootstrap.java:
   Line  51: logger.error("bootstrap.error")

📄 main\java\io\izzel\arclight\common\bridge\network\chat\ComponentBridgeHandler.java:
   Line  60: LOGGER.error("component.bridge.method-not-found")
   Line  63: LOGGER.error("component.bridge.init-failed")
   Line  91: LOGGER.error("component.bridge.get-siblings-failed")
   Line 128: LOGGER.error("component.bridge.create-stream-failed")
   Line 146: LOGGER.error("component.bridge.create-iterator-failed")

📄 main\java\io\izzel\arclight\common\mixin\bukkit\EntityTypeMixin.java:
   Line  57: LOGGER.warn("registry.entity.error")

📄 main\java\io\izzel\arclight\common\mixin\bukkit\MaterialMixin.java:
   Line 313: LOGGER.warn("material.bad-data-class")
   Line 402: LOGGER.warn("registry.block-state.error")
   Line 406: LOGGER.warn("registry.block-state.no-candidate")
   Line 473: LOGGER.warn("registry.meta-type.error")
   Line 477: LOGGER.warn("registry.meta-type.no-candidate")

📄 main\java\io\izzel\arclight\common\mixin\core\network\ServerLoginNetHandlerMixin.java:
   Line 129: LOGGER.error("player.place-in-world-failed")
   Line 151: LOGGER.info("velocity.forwarding.enabled-for-player")
   Line 173: LOGGER.error("velocity.query.send-failed")

📄 main\java\io\izzel\arclight\common\mixin\core\network\ServerPlayNetHandlerMixin.java:
   Line 371: LOGGER.warn("player.vehicle-moved-too-quickly")
   Line 399: LOGGER.warn("player.vehicle-moved-wrongly")
   Line 634: LOGGER.warn("player.moved-too-quickly")
   Line 667: LOGGER.warn("player.moved-wrongly")
   Line 789: ARCLIGHT_LOGGER.warn("player.dropped-items-quickly")
   ... and 7 more

📄 main\java\io\izzel\arclight\common\mixin\core\server\MinecraftServerMixin.java:
   Line 272: ARCLIGHT_LOGGER.info("server.starting")
   Line 278: ARCLIGHT_LOGGER.info("server.started")
   Line 292: ARCLIGHT_LOGGER.warn("server.overload-warning")
   Line 327: ARCLIGHT_LOGGER.info("server.stopping")
   Line 331: ARCLIGHT_LOGGER.error("server.unexpected-exception")
   ... and 10 more

📄 main\java\io\izzel\arclight\common\mixin\core\world\level\block\entity\SignBlockEntityMixin.java:
   Line  87: ARCLIGHT_LOGGER.warn("sign.non-editable-warning")

📄 main\java\io\izzel\arclight\common\mod\ArclightConnector.java:
   Line  20: LOGGER.info("mixin-load.core")
   Line  22: LOGGER.info("mixin-load.optimization")

📄 main\java\io\izzel\arclight\common\mod\ArclightMod.java:
   Line  31: LOGGER.info("mod-load")
   Line  54: LOGGER.info("server-initialization-completed")

📄 main\java\io\izzel\arclight\common\mod\server\ArclightServer.java:
   Line  72: LOGGER.info("registry.begin")
   Line  77: LOGGER.error("registry.error")

📄 main\java\io\izzel\arclight\common\mod\server\BukkitRegistry.java:
   Line 264: LOGGER.info("registry.biome")
   Line 289: LOGGER.info("registry.villager-profession")
   Line 309: LOGGER.info("registry.environment")
   Line 325: LOGGER.warn("registry.entity-type.not-found")
   Line 338: LOGGER.info("registry.entity-type")
   ... and 5 more

📄 main\java\io\izzel\arclight\common\mod\server\event\ArclightEventDispatcherRegistry.java:
   Line  16: LOGGER.info("registry.forge-event")

📄 main\java\io\izzel\arclight\common\mod\server\event\WorldEventDispatcher.java:
   Line  17: LOGGER.info("world.unloading")
   Line  19: LOGGER.info("world.unloaded")

📄 main\java\io\izzel\arclight\common\mod\server\world\WorldSymlink.java:
   Line  21: LOGGER.warn("symlink-file-exist")
   Line  27: LOGGER.warn("error-symlink")
   Line  29: LOGGER.error("symlink.create-error")

📄 main\java\io\izzel\arclight\common\mod\util\PluginEventHandler.java:
   Line 144: LOGGER.error("event.handler.registration-failed")

📄 main\java\io\izzel\arclight\common\mod\util\remapper\ArclightEnumExtender.java:
   Line  37: LOGGER.warn("enum.not-found-warning")
   Line  54: LOGGER.info("enum.field-added")

📄 main\java\io\izzel\arclight\common\mod\util\remapper\patcher\ArclightPluginPatcher.java:
   Line  37: LOGGER.info("patcher.loading")
   Line  46: LOGGER.info("patcher.loaded")

📄 main\java\io\izzel\arclight\common\mod\velocity\VelocityManager.java:
   Line  40: LOGGER.info("velocity.enabled")
   Line  43: LOGGER.info("velocity.disabled")
   Line  71: LOGGER.warn("velocity.failed-load-argument-types")

📄 main\java\io\izzel\arclight\common\optimization\mpem\AsyncEventSystem.java:
   Line 121: LOGGER.warn("optimization.async-event.disabled-due-to-errors")
   Line 124: LOGGER.error("optimization.async-event.handler-error")

📄 main\java\io\izzel\arclight\common\optimization\mpem\ChunkOptimizer.java:
   Line  54: LOGGER.info("optimization.chunk.unloaded")

📄 main\java\io\izzel\arclight\common\optimization\mpem\EntityCleaner.java:
   Line  67: LOGGER.info("optimization.entity-cleanup.cancelled")
   Line 108: LOGGER.info("optimization.entity-cleanup.starting")
   Line 138: LOGGER.info("optimization.entity-cleanup.mpem-completed")

📄 main\java\io\izzel\arclight\common\optimization\mpem\MemoryOptimizer.java:
   Line  34: LOGGER.warn("optimization.memory.high-usage")
   Line  37: LOGGER.info("optimization.memory.cleanup-start")
   Line  56: LOGGER.info("optimization.memory.cleanup-complete")
   Line  60: LOGGER.error("optimization.memory.cache-cleanup-error")
   Line  71: LOGGER.warn("optimization.memory.cache-cleanup-failed")

📄 main\java\io\izzel\arclight\common\optimization\mpem\MpemThreadManager.java:
   Line  70: LOGGER.warn("optimization.thread-pool.graceful-shutdown-failed")
   Line  73: LOGGER.error("optimization.thread-pool.forced-shutdown-failed")
   Line  78: LOGGER.warn("optimization.thread-pool.shutdown-interrupted")

📄 main\java\io\izzel\arclight\common\optimization\mpem\OptimizationManager.java:
   Line  53: LOGGER.error("optimization.manager.shutdown-error")

📄 main\java\io\izzel\arclight\common\optimization\mpem\async\AsyncAIManager.java:
   Line  78: LOGGER.warn("optimization.async-ai.calculation-error")
   Line 108: LOGGER.warn("optimization.async-ai.processing-error")

📄 main\java\io\izzel\arclight\common\optimization\mpem\async\AsyncCollisionSystem.java:
   Line  81: LOGGER.warn("optimization.async-collision.calculation-error")
   Line 117: LOGGER.warn("optimization.async-collision.processing-error")
   Line 158: LOGGER.warn("optimization.async-collision.check-error")

💡 RECOMMENDATIONS:
----------------------------------------
1. Convert hardcoded messages to i18n keys
2. Add corresponding translations to i18n files
3. Use ArclightI18nLogger instead of regular logger
4. Verify that potential i18n keys exist in translation files