#!/usr/bin/env python3
"""
I18n Cleanup Helper for Luminara Project

This script helps clean up unused i18n keys by generating cleanup suggestions
and optionally creating cleaned versions of i18n files.
"""

import os
import re
import shutil
from pathlib import Path
from typing import Set, Dict, List
import argparse


def extract_keys_from_hocon(file_path):
    """Extract keys from HOCON file with line numbers"""
    keys_with_lines = {}
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Remove comments section
    filtered_lines = []
    in_comments = False
    brace_count = 0
    
    for i, line in enumerate(lines):
        stripped = line.strip()
        
        if stripped.startswith('comments {') or stripped == 'comments {':
            in_comments = True
            brace_count = 1
            continue
        elif stripped == 'comments' and not in_comments:
            continue
        
        if in_comments:
            brace_count += line.count('{')
            brace_count -= line.count('}')
            if brace_count <= 0:
                in_comments = False
            continue
        
        filtered_lines.append((i + 1, line))
    
    # Extract keys with line numbers
    def extract_nested_keys(lines_with_nums, prefix=""):
        nested_keys = {}
        i = 0
        
        while i < len(lines_with_nums):
            line_num, line = lines_with_nums[i]
            stripped = line.strip()
            
            if not stripped or stripped.startswith('#'):
                i += 1
                continue
            
            key_pattern = r'^(\s*)([a-zA-Z0-9_-]+)\s*[={]'
            match = re.match(key_pattern, line)
            
            if match:
                key = match.group(2)
                if key.endswith('.comment'):
                    i += 1
                    continue
                
                full_key = f"{prefix}.{key}" if prefix else key
                nested_keys[full_key] = line_num
                
                if '{' in line:
                    brace_count = line.count('{') - line.count('}')
                    nested_lines = []
                    i += 1
                    
                    while i < len(lines_with_nums) and brace_count > 0:
                        nested_line_num, nested_line = lines_with_nums[i]
                        nested_lines.append((nested_line_num, nested_line))
                        brace_count += nested_line.count('{')
                        brace_count -= nested_line.count('}')
                        i += 1
                    
                    nested_keys.update(extract_nested_keys(nested_lines, full_key))
                else:
                    i += 1
            else:
                i += 1
        
        return nested_keys
    
    return extract_nested_keys(filtered_lines)


def find_used_keys(source_dirs):
    """Find used i18n keys in source code"""
    patterns = [
        r'LOGGER\.\w+\s*\(\s*"([^"]+)"',
        r'ArclightLocale\.\w+\s*\(\s*"([^"]+)"',
        r'ArclightLocale\.getInstance\(\)\.get\s*\(\s*"([^"]+)"',
        r'logger\.\w+\s*\(\s*"([^"]+)"',
        r'\.get\s*\(\s*"([^"]+)"',
    ]
    
    compiled_patterns = [re.compile(pattern, re.MULTILINE) for pattern in patterns]
    used_keys = set()
    
    for source_dir in source_dirs:
        if not os.path.exists(source_dir):
            continue
            
        for root, dirs, files in os.walk(source_dir):
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['build', 'target', 'out']]
            
            for file in files:
                if file.endswith(('.java', '.kt', '.scala')):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                        
                        for pattern in compiled_patterns:
                            matches = pattern.findall(content)
                            for match in matches:
                                # Filter out obvious non-keys
                                if (not match.startswith('.') and 
                                    not match.startswith('/') and 
                                    not match.endswith('.java') and
                                    not re.match(r'^[A-Z_]+$', match) and
                                    not re.match(r'^\d+$', match) and
                                    len(match) > 1 and
                                    ' ' not in match):
                                    used_keys.add(match)
                    
                    except Exception:
                        continue
    
    return used_keys


def generate_cleanup_suggestions(i18n_dir, source_dirs, output_file=None):
    """Generate cleanup suggestions"""
    i18n_path = Path(i18n_dir)
    i18n_files = list(i18n_path.glob('*.conf'))
    
    if not i18n_files:
        print(f"❌ No .conf files found in {i18n_dir}")
        return
    
    # Get used keys
    used_keys = find_used_keys(source_dirs)
    
    suggestions = []
    suggestions.append("I18N CLEANUP SUGGESTIONS")
    suggestions.append("=" * 50)
    suggestions.append("")
    
    total_unused = 0
    
    for file in i18n_files:
        print(f"📁 Analyzing {file.name}...")
        
        # Get all keys with line numbers
        keys_with_lines = extract_keys_from_hocon(str(file))
        
        # Find unused keys
        unused_keys = []
        for key, line_num in keys_with_lines.items():
            if key not in used_keys:
                unused_keys.append((key, line_num))
        
        if unused_keys:
            suggestions.append(f"📄 {file.name}")
            suggestions.append("-" * 30)
            suggestions.append(f"Total keys: {len(keys_with_lines)}")
            suggestions.append(f"Unused keys: {len(unused_keys)}")
            suggestions.append(f"Usage rate: {((len(keys_with_lines) - len(unused_keys)) / len(keys_with_lines) * 100):.1f}%")
            suggestions.append("")
            
            # Group by category
            categories = {}
            for key, line_num in unused_keys:
                if '.' in key:
                    category = key.split('.')[0]
                else:
                    category = 'root'
                
                if category not in categories:
                    categories[category] = []
                categories[category].append((key, line_num))
            
            for category, keys in sorted(categories.items()):
                suggestions.append(f"  {category.upper()}:")
                for key, line_num in sorted(keys):
                    suggestions.append(f"    Line {line_num:3d}: {key}")
                suggestions.append("")
            
            total_unused += len(unused_keys)
    
    suggestions.append("SUMMARY")
    suggestions.append("-" * 30)
    suggestions.append(f"Total unused keys across all files: {total_unused}")
    suggestions.append(f"Used keys: {len(used_keys)}")
    
    if total_unused > 0:
        suggestions.append("")
        suggestions.append("RECOMMENDATIONS:")
        suggestions.append("1. Review unused keys to ensure they're truly not needed")
        suggestions.append("2. Consider keeping error/warning keys for future use")
        suggestions.append("3. Remove keys that are definitely obsolete")
        suggestions.append("4. Update all language files consistently")
    
    report_text = "\n".join(suggestions)
    
    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report_text)
        print(f"📄 Cleanup suggestions saved to: {output_file}")
    else:
        print(report_text)


def create_cleaned_files(i18n_dir, source_dirs, output_dir, dry_run=True):
    """Create cleaned versions of i18n files"""
    i18n_path = Path(i18n_dir)
    output_path = Path(output_dir)
    
    if not dry_run:
        output_path.mkdir(exist_ok=True)
    
    i18n_files = list(i18n_path.glob('*.conf'))
    used_keys = find_used_keys(source_dirs)
    
    print(f"🧹 {'DRY RUN: ' if dry_run else ''}Creating cleaned i18n files...")
    
    for file in i18n_files:
        print(f"📁 Processing {file.name}...")
        
        with open(file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Extract keys with line numbers
        keys_with_lines = extract_keys_from_hocon(str(file))
        
        # Find lines to remove
        lines_to_remove = set()
        removed_count = 0
        
        for key, line_num in keys_with_lines.items():
            if key not in used_keys:
                lines_to_remove.add(line_num - 1)  # Convert to 0-based index
                removed_count += 1
        
        if not dry_run and lines_to_remove:
            # Create cleaned version
            cleaned_lines = []
            for i, line in enumerate(lines):
                if i not in lines_to_remove:
                    cleaned_lines.append(line)
            
            output_file = output_path / file.name
            with open(output_file, 'w', encoding='utf-8') as f:
                f.writelines(cleaned_lines)
            
            print(f"  ✅ Cleaned {file.name}: removed {removed_count} unused keys")
        else:
            print(f"  📊 Would remove {removed_count} unused keys from {file.name}")


def main():
    parser = argparse.ArgumentParser(description='I18n cleanup helper for Luminara project')
    parser.add_argument('--i18n-dir', default='i18n-config/src/main/resources/META-INF/i18n',
                       help='Directory containing i18n files')
    parser.add_argument('--source-dirs', nargs='+', 
                       default=['arclight-common/src', 'arclight-forge/src'],
                       help='Source directories to scan')
    parser.add_argument('--output', '-o', help='Output file for suggestions')
    parser.add_argument('--clean', action='store_true', 
                       help='Create cleaned versions of i18n files')
    parser.add_argument('--clean-output-dir', default='cleaned_i18n',
                       help='Output directory for cleaned files')
    parser.add_argument('--dry-run', action='store_true', default=True,
                       help='Dry run mode (default: true)')
    parser.add_argument('--force', action='store_true',
                       help='Actually create cleaned files (disables dry-run)')
    
    args = parser.parse_args()
    
    if args.force:
        args.dry_run = False
    
    if args.clean:
        create_cleaned_files(args.i18n_dir, args.source_dirs, args.clean_output_dir, args.dry_run)
    else:
        generate_cleanup_suggestions(args.i18n_dir, args.source_dirs, args.output)


if __name__ == '__main__':
    main()
