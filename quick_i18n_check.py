#!/usr/bin/env python3
"""
Quick I18n Usage Checker for Luminara Project

A simplified version for quick checks of i18n key usage.
"""

import os
import re
from pathlib import Path


def extract_keys_from_hocon(file_path):
    """Extract keys from HOCON file, ignoring comments section"""
    keys = set()
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Remove comments section
    lines = content.split('\n')
    result_lines = []
    in_comments = False
    brace_count = 0
    
    for line in lines:
        stripped = line.strip()
        
        if stripped.startswith('comments {') or stripped == 'comments {':
            in_comments = True
            brace_count = 1
            continue
        elif stripped == 'comments' and not in_comments:
            continue
        
        if in_comments:
            brace_count += line.count('{')
            brace_count -= line.count('}')
            if brace_count <= 0:
                in_comments = False
            continue
        
        result_lines.append(line)
    
    content = '\n'.join(result_lines)
    
    # Extract keys using simple regex
    key_pattern = r'^(\s*)([a-zA-Z0-9_-]+)\s*[={]'
    
    def extract_nested_keys(text, prefix=""):
        nested_keys = set()
        lines = text.split('\n')
        i = 0
        
        while i < len(lines):
            line = lines[i].strip()
            if not line or line.startswith('#'):
                i += 1
                continue
            
            match = re.match(key_pattern, lines[i])
            if match:
                key = match.group(2)
                if key.endswith('.comment'):
                    i += 1
                    continue
                
                full_key = f"{prefix}.{key}" if prefix else key
                nested_keys.add(full_key)
                
                if '{' in lines[i]:
                    brace_count = lines[i].count('{') - lines[i].count('}')
                    nested_content = []
                    i += 1
                    
                    while i < len(lines) and brace_count > 0:
                        nested_content.append(lines[i])
                        brace_count += lines[i].count('{')
                        brace_count -= lines[i].count('}')
                        i += 1
                    
                    nested_text = '\n'.join(nested_content)
                    nested_keys.update(extract_nested_keys(nested_text, full_key))
                else:
                    i += 1
            else:
                i += 1
        
        return nested_keys
    
    return extract_nested_keys(content)


def find_used_keys(source_dirs):
    """Find used i18n keys in source code"""
    patterns = [
        r'LOGGER\.\w+\s*\(\s*"([^"]+)"',
        r'ArclightLocale\.\w+\s*\(\s*"([^"]+)"',
        r'ArclightLocale\.getInstance\(\)\.get\s*\(\s*"([^"]+)"',
        r'logger\.\w+\s*\(\s*"([^"]+)"',
        r'\.get\s*\(\s*"([^"]+)"',
    ]
    
    compiled_patterns = [re.compile(pattern, re.MULTILINE) for pattern in patterns]
    used_keys = set()
    
    for source_dir in source_dirs:
        if not os.path.exists(source_dir):
            continue
            
        for root, dirs, files in os.walk(source_dir):
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['build', 'target', 'out']]
            
            for file in files:
                if file.endswith(('.java', '.kt', '.scala')):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                        
                        for pattern in compiled_patterns:
                            matches = pattern.findall(content)
                            for match in matches:
                                # Filter out obvious non-keys
                                if (not match.startswith('.') and 
                                    not match.startswith('/') and 
                                    not match.endswith('.java') and
                                    not re.match(r'^[A-Z_]+$', match) and
                                    not re.match(r'^\d+$', match) and
                                    len(match) > 1 and
                                    ' ' not in match):
                                    used_keys.add(match)
                    
                    except Exception:
                        continue
    
    return used_keys


def main():
    # Configuration
    i18n_dir = "i18n-config/src/main/resources/META-INF/i18n"
    source_dirs = ["arclight-common/src", "arclight-forge/src"]
    
    print("🔍 Quick I18n Usage Check")
    print("=" * 40)
    
    # Get defined keys
    defined_keys = set()
    i18n_files = list(Path(i18n_dir).glob('*.conf'))
    
    if not i18n_files:
        print(f"❌ No .conf files found in {i18n_dir}")
        return
    
    print(f"📁 Found {len(i18n_files)} i18n files")
    for file in i18n_files:
        keys = extract_keys_from_hocon(str(file))
        defined_keys.update(keys)
    
    print(f"📝 Total defined keys: {len(defined_keys)}")
    
    # Get used keys
    used_keys = find_used_keys(source_dirs)
    print(f"🔍 Total used keys: {len(used_keys)}")
    
    # Calculate unused
    unused_keys = defined_keys - used_keys
    undefined_used = used_keys - defined_keys
    
    print("\n📊 RESULTS:")
    print("-" * 40)
    print(f"✅ Used keys: {len(used_keys)}")
    print(f"⚠️  Unused keys: {len(unused_keys)}")
    print(f"❌ Undefined but used: {len(undefined_used)}")
    
    if unused_keys:
        print(f"\n🗑️  Top 10 unused keys:")
        for key in sorted(list(unused_keys))[:10]:
            print(f"   - {key}")
        if len(unused_keys) > 10:
            print(f"   ... and {len(unused_keys) - 10} more")
    
    if undefined_used:
        print(f"\n❓ Undefined but used keys:")
        for key in sorted(undefined_used):
            print(f"   - {key}")
    
    # Calculate efficiency
    if defined_keys:
        efficiency = (len(used_keys) / len(defined_keys)) * 100
        print(f"\n📈 I18n Efficiency: {efficiency:.1f}%")
        
        if efficiency >= 90:
            print("🎉 Excellent! Very few unused keys.")
        elif efficiency >= 70:
            print("👍 Good, but consider cleaning up unused keys.")
        else:
            print("⚠️  Many unused keys detected. Consider cleanup.")


if __name__ == '__main__':
    main()
