#!/usr/bin/env python3
"""
I18n Usage Checker for Luminara Project

This script checks which i18n keys are defined but not used in the source code.
It helps identify unused translations and missing translations.

Features:
- Parse HOCON i18n files and extract all keys (ignoring comments)
- Scan source code for i18n key usage patterns
- Generate detailed reports with categorized unused keys
- Filter out false positives
- Support for multiple i18n file formats
"""

import os
import re
import json
from pathlib import Path
from typing import Dict, Set, List, Tuple
import argparse


class HOCONParser:
    """Simple HOCON parser for i18n files"""
    
    def __init__(self):
        self.keys = set()
    
    def parse_file(self, file_path: str) -> Set[str]:
        """Parse HOCON file and extract all keys, ignoring comments section"""
        keys = set()
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Remove comments section entirely
        content = self._remove_comments_section(content)
        
        # Extract keys recursively
        keys.update(self._extract_keys(content))
        
        return keys
    
    def _remove_comments_section(self, content: str) -> str:
        """Remove the comments section from HOCON content"""
        lines = content.split('\n')
        result_lines = []
        in_comments = False
        brace_count = 0
        
        for line in lines:
            stripped = line.strip()
            
            # Check if we're entering comments section
            if stripped.startswith('comments {') or stripped == 'comments {':
                in_comments = True
                brace_count = 1
                continue
            elif stripped == 'comments' and not in_comments:
                # Look ahead for opening brace
                continue
            
            if in_comments:
                # Count braces to know when comments section ends
                brace_count += line.count('{')
                brace_count -= line.count('}')
                
                if brace_count <= 0:
                    in_comments = False
                continue
            
            result_lines.append(line)
        
        return '\n'.join(result_lines)
    
    def _extract_keys(self, content: str, prefix: str = "") -> Set[str]:
        """Extract keys from HOCON content recursively"""
        keys = set()
        
        # Remove comments and strings to avoid false matches
        content = self._clean_content(content)
        
        # Find key-value pairs
        # Pattern for key = value or key { ... }
        key_pattern = r'^(\s*)([a-zA-Z0-9_-]+)\s*[={]'
        
        lines = content.split('\n')
        i = 0
        
        while i < len(lines):
            line = lines[i].strip()
            if not line or line.startswith('#'):
                i += 1
                continue
            
            match = re.match(key_pattern, lines[i])
            if match:
                indent = len(match.group(1))
                key = match.group(2)
                
                # Skip if this is a comment key
                if key.endswith('.comment'):
                    i += 1
                    continue
                
                full_key = f"{prefix}.{key}" if prefix else key
                keys.add(full_key)
                
                # Check if this is a nested object
                if '{' in lines[i]:
                    # Find the matching closing brace
                    brace_count = lines[i].count('{') - lines[i].count('}')
                    nested_content = []
                    i += 1
                    
                    while i < len(lines) and brace_count > 0:
                        nested_content.append(lines[i])
                        brace_count += lines[i].count('{')
                        brace_count -= lines[i].count('}')
                        i += 1
                    
                    # Recursively extract keys from nested content
                    nested_text = '\n'.join(nested_content)
                    nested_keys = self._extract_keys(nested_text, full_key)
                    keys.update(nested_keys)
                else:
                    i += 1
            else:
                i += 1
        
        return keys
    
    def _clean_content(self, content: str) -> str:
        """Remove strings and comments to avoid false key matches"""
        # Remove string literals
        content = re.sub(r'"[^"]*"', '""', content)
        content = re.sub(r"'[^']*'", "''", content)
        
        # Remove line comments
        lines = content.split('\n')
        cleaned_lines = []
        for line in lines:
            # Remove comments but preserve the line structure
            if '#' in line:
                line = line[:line.index('#')]
            cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)


class SourceCodeScanner:
    """Scanner for finding i18n key usage in source code"""
    
    def __init__(self):
        # Patterns to match i18n key usage
        self.patterns = [
            # LOGGER.info("key"), LOGGER.error("key"), etc.
            r'LOGGER\.\w+\s*\(\s*"([^"]+)"',
            # ArclightLocale.info("key"), ArclightLocale.error("key")
            r'ArclightLocale\.\w+\s*\(\s*"([^"]+)"',
            # ArclightLocale.getInstance().get("key")
            r'ArclightLocale\.getInstance\(\)\.get\s*\(\s*"([^"]+)"',
            # logger.info("key") where logger is ArclightI18nLogger
            r'logger\.\w+\s*\(\s*"([^"]+)"',
            # Direct string usage in i18n context
            r'\.get\s*\(\s*"([^"]+)"',
            # More specific patterns for better matching
            r'ArclightI18nLogger\.getLogger\([^)]+\)\.\w+\s*\(\s*"([^"]+)"',
            # Pattern for format() calls
            r'\.format\s*\(\s*"([^"]+)"',
        ]

        # Patterns to exclude (these are likely not i18n keys)
        self.exclude_patterns = [
            r'^[A-Z_]+$',  # All caps constants
            r'^\d+$',      # Numbers only
            r'^[a-z]+://.*',  # URLs
            r'.*\.(java|class|jar|xml|json)$',  # File extensions
            r'^/.*',       # Paths starting with /
            r'.*\s.*',     # Contains spaces (likely not keys)
            r'^[^a-zA-Z].*',  # Doesn't start with letter
        ]

        self.compiled_patterns = [re.compile(pattern, re.MULTILINE) for pattern in self.patterns]
        self.compiled_exclude_patterns = [re.compile(pattern) for pattern in self.exclude_patterns]
    
    def scan_directory(self, directory: str, extensions: List[str] = None) -> Set[str]:
        """Scan directory for i18n key usage"""
        if extensions is None:
            extensions = ['.java', '.kt', '.scala']
        
        used_keys = set()
        
        for root, dirs, files in os.walk(directory):
            # Skip build directories and other non-source directories
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['build', 'target', 'out']]
            
            for file in files:
                if any(file.endswith(ext) for ext in extensions):
                    file_path = os.path.join(root, file)
                    used_keys.update(self._scan_file(file_path))
        
        return used_keys
    
    def _scan_file(self, file_path: str) -> Set[str]:
        """Scan a single file for i18n key usage"""
        used_keys = set()

        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            for pattern in self.compiled_patterns:
                matches = pattern.findall(content)
                for match in matches:
                    # Filter out matches that are likely not i18n keys
                    if not self._should_exclude_key(match):
                        used_keys.add(match)

        except Exception as e:
            print(f"Warning: Could not read file {file_path}: {e}")

        return used_keys

    def _should_exclude_key(self, key: str) -> bool:
        """Check if a key should be excluded based on exclude patterns"""
        for pattern in self.compiled_exclude_patterns:
            if pattern.match(key):
                return True
        return False


class I18nChecker:
    """Main class for checking i18n usage"""
    
    def __init__(self, i18n_dir: str, source_dirs: List[str]):
        self.i18n_dir = Path(i18n_dir)
        self.source_dirs = [Path(d) for d in source_dirs]
        self.parser = HOCONParser()
        self.scanner = SourceCodeScanner()
    
    def check_usage(self) -> Tuple[Set[str], Set[str], Set[str], Dict[str, str]]:
        """Check i18n usage and return (defined_keys, used_keys, unused_keys, key_sources)"""

        # Get all defined keys from i18n files
        defined_keys = set()
        key_sources = {}  # Track which file each key comes from
        i18n_files = list(self.i18n_dir.glob('*.conf'))

        if not i18n_files:
            print(f"Warning: No .conf files found in {self.i18n_dir}")
            return set(), set(), set(), {}

        print(f"Found {len(i18n_files)} i18n files:")
        for file in i18n_files:
            print(f"  - {file.name}")
            keys = self.parser.parse_file(str(file))
            defined_keys.update(keys)
            # Track source file for each key
            for key in keys:
                if key not in key_sources:
                    key_sources[key] = file.name
            print(f"    Extracted {len(keys)} keys")

        print(f"\nTotal defined keys: {len(defined_keys)}")

        # Get all used keys from source code
        used_keys = set()
        print(f"\nScanning source directories:")
        for source_dir in self.source_dirs:
            if source_dir.exists():
                print(f"  - {source_dir}")
                keys = self.scanner.scan_directory(str(source_dir))
                used_keys.update(keys)
                print(f"    Found {len(keys)} key usages")
            else:
                print(f"  - {source_dir} (not found)")

        print(f"\nTotal used keys: {len(used_keys)}")

        # Find unused keys
        unused_keys = defined_keys - used_keys

        return defined_keys, used_keys, unused_keys, key_sources
    
    def generate_report(self, output_file: str = None, detailed: bool = False):
        """Generate usage report"""
        defined_keys, used_keys, unused_keys = self.check_usage()

        # Also find keys used but not defined
        undefined_used_keys = used_keys - defined_keys

        report = []
        report.append("=" * 60)
        report.append("I18N USAGE REPORT")
        report.append("=" * 60)
        report.append(f"Total defined keys: {len(defined_keys)}")
        report.append(f"Total used keys: {len(used_keys)}")
        report.append(f"Unused keys: {len(unused_keys)}")
        report.append(f"Undefined but used keys: {len(undefined_used_keys)}")
        report.append("")

        if unused_keys:
            report.append("UNUSED KEYS:")
            report.append("-" * 40)
            # Group by category for better readability
            categorized_unused = self._categorize_keys(unused_keys)
            for category, keys in sorted(categorized_unused.items()):
                if keys:
                    report.append(f"\n  {category.upper()}:")
                    for key in sorted(keys):
                        report.append(f"    {key}")
            report.append("")

        if undefined_used_keys:
            report.append("UNDEFINED BUT USED KEYS:")
            report.append("-" * 40)
            # Filter out likely false positives
            filtered_undefined = self._filter_undefined_keys(undefined_used_keys)
            for key in sorted(filtered_undefined):
                report.append(f"  {key}")
            report.append("")

            if len(filtered_undefined) < len(undefined_used_keys):
                report.append(f"Note: Filtered out {len(undefined_used_keys) - len(filtered_undefined)} likely false positives")
                report.append("")

        report.append("SUMMARY:")
        report.append("-" * 40)
        if not unused_keys and not undefined_used_keys:
            report.append("✅ All i18n keys are properly used!")
        else:
            if unused_keys:
                report.append(f"⚠️  {len(unused_keys)} keys are defined but not used")
            if undefined_used_keys:
                filtered_count = len(self._filter_undefined_keys(undefined_used_keys))
                report.append(f"❌ {filtered_count} keys are used but not defined (after filtering)")

        report_text = "\n".join(report)

        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_text)
            print(f"Report saved to: {output_file}")
        else:
            print(report_text)

    def _categorize_keys(self, keys: Set[str]) -> Dict[str, List[str]]:
        """Categorize keys by their prefix for better organization"""
        categories = {}
        for key in keys:
            if '.' in key:
                category = key.split('.')[0]
            else:
                category = 'root'

            if category not in categories:
                categories[category] = []
            categories[category].append(key)

        return categories

    def _filter_undefined_keys(self, keys: Set[str]) -> Set[str]:
        """Filter out keys that are likely false positives"""
        filtered = set()
        for key in keys:
            # Skip keys that are likely not i18n keys
            if (not key.startswith('.') and
                not key.startswith('/') and
                not key.endswith('.java') and
                not key.endswith('.class') and
                not re.match(r'^[A-Z_]+$', key) and  # All caps constants
                not re.match(r'^\d+$', key) and     # Numbers only
                len(key) > 1 and
                not ' ' in key):  # No spaces
                filtered.add(key)

        return filtered


def main():
    parser = argparse.ArgumentParser(description='Check i18n key usage in Luminara project')
    parser.add_argument('--i18n-dir', default='i18n-config/src/main/resources/META-INF/i18n',
                       help='Directory containing i18n files')
    parser.add_argument('--source-dirs', nargs='+',
                       default=['arclight-common/src', 'arclight-forge/src', 'arclight-fabric/src', 'arclight-neoforge/src'],
                       help='Source directories to scan')
    parser.add_argument('--output', '-o', help='Output file for report')
    parser.add_argument('--detailed', '-d', action='store_true',
                       help='Generate detailed report with categorized keys')

    args = parser.parse_args()

    checker = I18nChecker(args.i18n_dir, args.source_dirs)
    checker.generate_report(args.output, args.detailed)


if __name__ == '__main__':
    main()
