============================================================
I18N USAGE REPORT
============================================================
Total defined keys: 146
Total used keys: 230
Unused keys: 68
Undefined but used keys: 152

UNUSED KEYS:
----------------------------------------
  Implementer
  Implementer.error
  Implementer.not-found
  chat
  chat.illegal-characters
  chat.message-too-long
  chat.player-removed
  dist
  dist.logic-world-check
  enum
  error
  error.class-not-found
  error.database-error
  error.field-not-found
  error.file-not-found
  error.invalid-configuration
  error.method-not-found
  error.mixin-error
  error.network-error
  error.permission-denied
  error.plugin-error
  i18n
  i18n.current-not-available
  implementer
  implementer.error
  implementer.not-found
  java
  loading-mapping
  mixin-load
  mod
  mod.conflict-detected
  optimization
  optimization.async-ai
  optimization.async-collision
  optimization.async-event
  optimization.chunk
  optimization.entity-cleanup
  optimization.manager
  optimization.memory
  patcher
  player
  player.dropped-items-disconnect
  player.internal-command-error
  player.invalid-hotbar-disconnect
  registry
  registry.block-state
  registry.block-state.not-subclass
  registry.entity
  registry.entity.not-subclass
  registry.meta-type
  registry.meta-type.not-subclass
  release-name
  release-name.Executions
  release-name.GreatHorn
  release-name.Horn
  release-name.Trials
  server
  sign
  symlink
  velocity
  warning
  warning.async-operation
  warning.deprecated-api
  warning.disk-space-low
  warning.memory-low
  warning.performance-issue
  warning.plugin-conflict
  world

UNDEFINED BUT USED KEYS:
----------------------------------------
  .arclight
  .arclight/class_cache
  Added {} to {}
  All worlds saved successfully
  Assign {} to unknown level stem {}
  Async world save did not complete within timeout or failed
  Authentication servers are down but will let them in anyway!
  Bad material data class {} for {}
  Bukkit.MaxHealth
  BukkitValues
  Checking Velocity packet integrity, readable bytes: {}
  ChunkBukkitValues
  Command argument type {} is not registered
  ComponentBridgeHandler initialized successfully with method: 
  Could not find getSiblings method in Component class
  Couldn't dispatch custom payload
  Couldn't place player in world
  Couldn't register custom payload
  Couldn't unregister custom payload
  Couldn't verify username because servers are unavailable
  Created profile for player: {}
  Custom payload REGISTER string too long: {} characters
  Custom payload REGISTER too large: {} bytes
  Custom payload UNREGISTER string too long: {} characters
  Custom payload UNREGISTER too large: {} bytes
  Data length: {}
  Definalize enum class {} values field {}
  Dragon is now in phase {} on the {}
  Error bootstrap Arclight
  Error calculating redstone signal
  Error handling async collision between {} and {}
  Error in AI calculations
  Error in collision calculations
  Error processing async redstone update at {}
  Error registering event handler: {} {}
  Error verifying Velocity signature
  Exception processing Velocity forwarding packet from {}
  Exception processing Velocity login for 
  Exception stopping the server
  Exception verifying 
  Exception verifying {} 
  Expected signature length: {}, actual signature length: {}
  Expected: {}
  Failed to create Component iterator: 
  Failed to create Component stream: 
  Failed to dump class 
  Failed to find class {}
  Failed to get siblings from Component: 
  Failed to handle packet {}, suppressing error
  Failed to initialize ComponentBridgeHandler: 
  Failed to load player data for 
  Failed to register enchantment {}: {}
  Failed to register potion type {}: {}
  Failed to save world {}
  Failed to schedule load callback for chunk 
  Failed to schedule unload callback for chunk 
  Failed to send Velocity query packet
  Failed to set address via bridge
  Failed to set address via reflection, trying bridge method
  Failed to set forwarded address via f_129469_, trying alternatives
  Failed to verify username but will let them in anyway!
  Forcing entity cleanup...
  Forwarded address: {}
  Forwarding forge permission[{}] to bukkit
  Generated {} redirecting to {}
  Got: {}
  Ignoring packet due to disconnection: 
  Interrupted during shutdown
  Interrupted while shutting down {} pool
  Loaded {} recipes
  Loading CLIENT side class: {}
  Mismatch in destroy block pos: 
  No class remap config is provided for class {}, using PLUGIN
  Not found {} in {}
  Online-mode disabled, skipping Mojang authentication for: {}
  Online-mode enabled, proceeding with Mojang authentication for: {}
  Online-mode is disabled, trusting forwarded profile from Velocity
  Online-mode is enabled, profile will be verified through Mojang servers
  Parsing error loading recipe {}
  Player {} interacted with invalid menu {}
  Player {} tried to attack an invalid entity
  Player-data validated!
  Preparing start region for dimension {}
  Processing Velocity forwarding data, buffer size: {}
  Processing Velocity forwarding packet, readable bytes: {}
  Read data: {} bytes
  Read signature: {} bytes
  Received Velocity query response with null data
  Received custom query packet - ID: {}, Expected: {}, HasData: {}
  Registered sync event: {}
  Registered {} as art {}
  Registered {} as biome {}
  Registered {} as block {}
  Registered {} as cooking category {}
  Registered {} as custom stats {}
  Registered {} as enchantment {}
  Registered {} as ender dragon phase {}
  Registered {} as entity {}
  Registered {} as environment {}
  Registered {} as fluid {}
  Registered {} as illager spell {}
  Registered {} as item {}
  Registered {} as pose {}
  Registered {} as potion type {}
  Registered {} as potion {}
  Registered {} as spawn category {}
  Registered {} as stats {}
  Registered {} as villager profession {}
  Scheduled entity cleanup cancelled
  Secret: {}
  Sent Velocity query packet with ID: {} for player: {}
  Set forwarded address via address field: {}:{}
  Set forwarded address via f_129469_: {}:{}
  Skip jij dependency {}@{} because Luminara has {}
  Skipping loading recipe {} as it's conditions were not met
  Skipping loading recipe {} as it's serializer returned null
  Starting async world save during server shutdown...
  Starting async world save...
  Successfully processed Velocity forwarding for player: {}
  Successfully processed Velocity forwarding for player: {} (online-mode: {})
  Threads {} not shutting down
  Tried to over-fill a container
  UUID of player {} is {}
  UUID of player {} is {} (from Velocity)
  Unable to create a portal, likely target out of worldborder
  Uncaught exception in thread {}
  Unhandled block break event
  Unhandled damage of {} by {} from {}
  Unhandled damage of {} from {}
  Unhandled secondary block break event
  Unknown target reason setting {} target to {}
  Username '{}' tried to join with an invalid session
  Velocity Modern Forwarding initialized
  Velocity Modern Forwarding is enabled for player: {} (online-mode: {})
  Velocity forwarding version: {}
  Velocity packet too small: {} bytes
  Velocity signature mismatch!
  Velocity signature verification successful
  dimension
  entries
  forgeCaps
  release-name.
  {} (vehicle of {}) moved too quickly! {},{},{}
  {} (vehicle of {}) moved wrongly! {}
  {} delegates to field {}
  {} delegates to nothing
  {} is sending move packets too frequently ({} packets since last tick)
  {} moved too quickly! {},{},{}
  {} moved wrongly!
  {} pool did not terminate after forced shutdown
  {} pool did not terminate gracefully, forcing shutdown
  {} threads not shutting down correctly, force exiting

SUMMARY:
----------------------------------------
⚠️  68 keys are defined but not used
❌ 152 keys are used but not defined