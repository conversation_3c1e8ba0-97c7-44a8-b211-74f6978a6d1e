I18N CLEANUP SUGGESTIONS
==================================================

📄 en_us.conf
------------------------------
Total keys: 142
Unused keys: 65
Usage rate: 54.2%

  CHAT:
    Line 206: chat.illegal-characters
    Line 203: chat.message-too-long
    Line 207: chat.player-removed

  DIST:
    Line 198: dist.logic-world-check

  ERROR:
    Line 141: error.class-not-found
    Line 148: error.database-error
    Line 143: error.field-not-found
    Line 145: error.file-not-found
    Line 144: error.invalid-configuration
    Line 142: error.method-not-found
    Line 150: error.mixin-error
    Line 147: error.network-error
    Line 146: error.permission-denied
    Line 149: error.plugin-error

  I18N:
    Line  36: i18n.current-not-available

  IMPLEMENTER:
    Line  33: implementer.error
    Line  32: implementer.not-found

  MOD:
    Line 178: mod.conflict-detected

  OPTIMIZATION:
    Line 115: optimization.async-ai
    Line 119: optimization.async-collision
    Line 124: optimization.async-event
    Line  98: optimization.chunk
    Line  93: optimization.entity-cleanup
    Line 112: optimization.manager
    Line 103: optimization.memory

  PLAYER:
    Line 213: player.dropped-items-disconnect
    Line 217: player.internal-command-error
    Line 215: player.invalid-hotbar-disconnect

  REGISTRY:
    Line  66: registry.block-state
    Line  67: registry.block-state.not-subclass
    Line  71: registry.entity
    Line  72: registry.entity.not-subclass
    Line  61: registry.meta-type
    Line  62: registry.meta-type.not-subclass

  RELEASE-NAME:
    Line  20: release-name.Executions
    Line  19: release-name.GreatHorn
    Line  18: release-name.Horn
    Line  21: release-name.Trials

  ROOT:
    Line 202: chat
    Line 197: dist
    Line 187: enum
    Line 140: error
    Line  35: i18n
    Line  31: implementer
    Line  23: java
    Line  39: loading-mapping
    Line  40: mixin-load
    Line 177: mod
    Line  92: optimization
    Line  45: patcher
    Line 211: player
    Line  50: registry
    Line  17: release-name
    Line  80: server
    Line 182: sign
    Line 192: symlink
    Line 132: velocity
    Line 154: warning
    Line 164: world

  WARNING:
    Line 160: warning.async-operation
    Line 155: warning.deprecated-api
    Line 158: warning.disk-space-low
    Line 157: warning.memory-low
    Line 156: warning.performance-issue
    Line 159: warning.plugin-conflict

📄 es_es.conf
------------------------------
Total keys: 142
Unused keys: 65
Usage rate: 54.2%

  CHAT:
    Line 204: chat.illegal-characters
    Line 201: chat.message-too-long
    Line 205: chat.player-removed

  DIST:
    Line 196: dist.logic-world-check

  ERROR:
    Line 139: error.class-not-found
    Line 146: error.database-error
    Line 141: error.field-not-found
    Line 143: error.file-not-found
    Line 142: error.invalid-configuration
    Line 140: error.method-not-found
    Line 148: error.mixin-error
    Line 145: error.network-error
    Line 144: error.permission-denied
    Line 147: error.plugin-error

  I18N:
    Line  34: i18n.current-not-available

  IMPLEMENTER:
    Line  31: implementer.error
    Line  30: implementer.not-found

  MOD:
    Line 176: mod.conflict-detected

  OPTIMIZATION:
    Line 113: optimization.async-ai
    Line 117: optimization.async-collision
    Line 122: optimization.async-event
    Line  96: optimization.chunk
    Line  91: optimization.entity-cleanup
    Line 110: optimization.manager
    Line 101: optimization.memory

  PLAYER:
    Line 211: player.dropped-items-disconnect
    Line 215: player.internal-command-error
    Line 213: player.invalid-hotbar-disconnect

  REGISTRY:
    Line  64: registry.block-state
    Line  65: registry.block-state.not-subclass
    Line  69: registry.entity
    Line  70: registry.entity.not-subclass
    Line  59: registry.meta-type
    Line  60: registry.meta-type.not-subclass

  RELEASE-NAME:
    Line  18: release-name.Executions
    Line  17: release-name.GreatHorn
    Line  16: release-name.Horn
    Line  19: release-name.Trials

  ROOT:
    Line 200: chat
    Line 195: dist
    Line 185: enum
    Line 138: error
    Line  33: i18n
    Line  29: implementer
    Line  21: java
    Line  37: loading-mapping
    Line  38: mixin-load
    Line 175: mod
    Line  90: optimization
    Line  43: patcher
    Line 209: player
    Line  48: registry
    Line  15: release-name
    Line  78: server
    Line 180: sign
    Line 190: symlink
    Line 130: velocity
    Line 152: warning
    Line 162: world

  WARNING:
    Line 158: warning.async-operation
    Line 153: warning.deprecated-api
    Line 156: warning.disk-space-low
    Line 155: warning.memory-low
    Line 154: warning.performance-issue
    Line 157: warning.plugin-conflict

📄 fr_fr.conf
------------------------------
Total keys: 140
Unused keys: 64
Usage rate: 54.3%

  CHAT:
    Line 197: chat.illegal-characters
    Line 194: chat.message-too-long
    Line 198: chat.player-removed

  DIST:
    Line 189: dist.logic-world-check

  ERROR:
    Line 132: error.class-not-found
    Line 139: error.database-error
    Line 134: error.field-not-found
    Line 136: error.file-not-found
    Line 135: error.invalid-configuration
    Line 133: error.method-not-found
    Line 141: error.mixin-error
    Line 138: error.network-error
    Line 137: error.permission-denied
    Line 140: error.plugin-error

  I18N:
    Line  27: i18n.current-not-available

  IMPLEMENTER:
    Line  24: implementer.error
    Line  23: implementer.not-found

  MOD:
    Line 169: mod.conflict-detected

  OPTIMIZATION:
    Line 106: optimization.async-ai
    Line 110: optimization.async-collision
    Line 115: optimization.async-event
    Line  89: optimization.chunk
    Line  84: optimization.entity-cleanup
    Line 103: optimization.manager
    Line  94: optimization.memory

  PLAYER:
    Line 204: player.dropped-items-disconnect
    Line 208: player.internal-command-error
    Line 206: player.invalid-hotbar-disconnect

  REGISTRY:
    Line  57: registry.block-state
    Line  58: registry.block-state.not-subclass
    Line  62: registry.entity
    Line  63: registry.entity.not-subclass
    Line  52: registry.meta-type
    Line  53: registry.meta-type.not-subclass

  RELEASE-NAME:
    Line  18: release-name.Executions
    Line  17: release-name.GreatHorn
    Line  16: release-name.Horn
    Line  19: release-name.Trials

  ROOT:
    Line 193: chat
    Line 188: dist
    Line 178: enum
    Line 131: error
    Line  26: i18n
    Line  22: implementer
    Line  30: loading-mapping
    Line  31: mixin-load
    Line 168: mod
    Line  83: optimization
    Line  36: patcher
    Line 202: player
    Line  41: registry
    Line  15: release-name
    Line  71: server
    Line 173: sign
    Line 183: symlink
    Line 123: velocity
    Line 145: warning
    Line 155: world

  WARNING:
    Line 151: warning.async-operation
    Line 146: warning.deprecated-api
    Line 149: warning.disk-space-low
    Line 148: warning.memory-low
    Line 147: warning.performance-issue
    Line 150: warning.plugin-conflict

📄 ko_kr.conf
------------------------------
Total keys: 142
Unused keys: 65
Usage rate: 54.2%

  CHAT:
    Line 208: chat.illegal-characters
    Line 205: chat.message-too-long
    Line 209: chat.player-removed

  DIST:
    Line 200: dist.logic-world-check

  ERROR:
    Line 143: error.class-not-found
    Line 150: error.database-error
    Line 145: error.field-not-found
    Line 147: error.file-not-found
    Line 146: error.invalid-configuration
    Line 144: error.method-not-found
    Line 152: error.mixin-error
    Line 149: error.network-error
    Line 148: error.permission-denied
    Line 151: error.plugin-error

  I18N:
    Line  38: i18n.current-not-available

  IMPLEMENTER:
    Line  35: implementer.error
    Line  34: implementer.not-found

  MOD:
    Line 180: mod.conflict-detected

  OPTIMIZATION:
    Line 117: optimization.async-ai
    Line 121: optimization.async-collision
    Line 126: optimization.async-event
    Line 100: optimization.chunk
    Line  95: optimization.entity-cleanup
    Line 114: optimization.manager
    Line 105: optimization.memory

  PLAYER:
    Line 215: player.dropped-items-disconnect
    Line 219: player.internal-command-error
    Line 217: player.invalid-hotbar-disconnect

  REGISTRY:
    Line  68: registry.block-state
    Line  69: registry.block-state.not-subclass
    Line  73: registry.entity
    Line  74: registry.entity.not-subclass
    Line  63: registry.meta-type
    Line  64: registry.meta-type.not-subclass

  RELEASE-NAME:
    Line  22: release-name.Executions
    Line  21: release-name.GreatHorn
    Line  20: release-name.Horn
    Line  23: release-name.Trials

  ROOT:
    Line 204: chat
    Line 199: dist
    Line 189: enum
    Line 142: error
    Line  37: i18n
    Line  33: implementer
    Line  25: java
    Line  41: loading-mapping
    Line  42: mixin-load
    Line 179: mod
    Line  94: optimization
    Line  47: patcher
    Line 213: player
    Line  52: registry
    Line  19: release-name
    Line  82: server
    Line 184: sign
    Line 194: symlink
    Line 134: velocity
    Line 156: warning
    Line 166: world

  WARNING:
    Line 162: warning.async-operation
    Line 157: warning.deprecated-api
    Line 160: warning.disk-space-low
    Line 159: warning.memory-low
    Line 158: warning.performance-issue
    Line 161: warning.plugin-conflict

📄 ru_ru.conf
------------------------------
Total keys: 142
Unused keys: 65
Usage rate: 54.2%

  IMPLEMENTER:
    Line  31: Implementer.error
    Line  30: Implementer.not-found

  CHAT:
    Line 204: chat.illegal-characters
    Line 201: chat.message-too-long
    Line 205: chat.player-removed

  DIST:
    Line 196: dist.logic-world-check

  ERROR:
    Line 139: error.class-not-found
    Line 146: error.database-error
    Line 141: error.field-not-found
    Line 143: error.file-not-found
    Line 142: error.invalid-configuration
    Line 140: error.method-not-found
    Line 148: error.mixin-error
    Line 145: error.network-error
    Line 144: error.permission-denied
    Line 147: error.plugin-error

  I18N:
    Line  34: i18n.current-not-available

  MOD:
    Line 176: mod.conflict-detected

  OPTIMIZATION:
    Line 113: optimization.async-ai
    Line 117: optimization.async-collision
    Line 122: optimization.async-event
    Line  96: optimization.chunk
    Line  91: optimization.entity-cleanup
    Line 110: optimization.manager
    Line 101: optimization.memory

  PLAYER:
    Line 211: player.dropped-items-disconnect
    Line 215: player.internal-command-error
    Line 213: player.invalid-hotbar-disconnect

  REGISTRY:
    Line  64: registry.block-state
    Line  65: registry.block-state.not-subclass
    Line  69: registry.entity
    Line  70: registry.entity.not-subclass
    Line  59: registry.meta-type
    Line  60: registry.meta-type.not-subclass

  RELEASE-NAME:
    Line  18: release-name.Executions
    Line  17: release-name.GreatHorn
    Line  16: release-name.Horn
    Line  19: release-name.Trials

  ROOT:
    Line  29: Implementer
    Line 200: chat
    Line 195: dist
    Line 185: enum
    Line 138: error
    Line  33: i18n
    Line  21: java
    Line  37: loading-mapping
    Line  38: mixin-load
    Line 175: mod
    Line  90: optimization
    Line  43: patcher
    Line 209: player
    Line  48: registry
    Line  15: release-name
    Line  78: server
    Line 180: sign
    Line 190: symlink
    Line 130: velocity
    Line 152: warning
    Line 162: world

  WARNING:
    Line 158: warning.async-operation
    Line 153: warning.deprecated-api
    Line 156: warning.disk-space-low
    Line 155: warning.memory-low
    Line 154: warning.performance-issue
    Line 157: warning.plugin-conflict

📄 zh_cn.conf
------------------------------
Total keys: 125
Unused keys: 47
Usage rate: 62.4%

  CHAT:
    Line 183: chat.illegal-characters
    Line 180: chat.message-too-long
    Line 184: chat.player-removed

  DIST:
    Line 175: dist.logic-world-check

  I18N:
    Line  34: i18n.current-not-available

  IMPLEMENTER:
    Line  31: implementer.error
    Line  30: implementer.not-found

  MOD:
    Line 155: mod.conflict-detected

  OPTIMIZATION:
    Line 114: optimization.async-ai
    Line 118: optimization.async-collision
    Line 123: optimization.async-event
    Line  97: optimization.chunk
    Line  92: optimization.entity-cleanup
    Line 111: optimization.manager
    Line 102: optimization.memory

  PLAYER:
    Line 190: player.dropped-items-disconnect
    Line 194: player.internal-command-error
    Line 192: player.invalid-hotbar-disconnect

  REGISTRY:
    Line  65: registry.block-state
    Line  66: registry.block-state.not-subclass
    Line  70: registry.entity
    Line  71: registry.entity.not-subclass
    Line  60: registry.meta-type
    Line  61: registry.meta-type.not-subclass

  RELEASE-NAME:
    Line  18: release-name.Executions
    Line  17: release-name.GreatHorn
    Line  16: release-name.Horn
    Line  19: release-name.Trials

  ROOT:
    Line 179: chat
    Line 174: dist
    Line 164: enum
    Line  33: i18n
    Line  29: implementer
    Line  21: java
    Line  37: loading-mapping
    Line  38: mixin-load
    Line 154: mod
    Line  91: optimization
    Line  44: patcher
    Line 188: player
    Line  49: registry
    Line  15: release-name
    Line  79: server
    Line 159: sign
    Line 169: symlink
    Line 131: velocity
    Line 141: world

SUMMARY
------------------------------
Total unused keys across all files: 371
Used keys: 85

RECOMMENDATIONS:
1. Review unused keys to ensure they're truly not needed
2. Consider keeping error/warning keys for future use
3. Remove keys that are definitely obsolete
4. Update all language files consistently