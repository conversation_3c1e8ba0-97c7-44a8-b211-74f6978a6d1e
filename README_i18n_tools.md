# I18n Usage Checker Tools for Luminara

这是为Luminara项目创建的i18n键值使用情况检查工具集，用于查漏补缺和优化国际化配置。

## 📁 文件结构

### 核心工具
- `check_i18n_usage.py` - 完整分析工具
- `quick_i18n_check.py` - 快速检查工具
- `i18n_cleanup_helper.py` - 清理助手工具
- `run_i18n_check.bat` - Windows批处理菜单

### 生成的报告
- `i18n_analysis_report.txt` - 完整分析报告
- `i18n_cleanup_detailed.txt` - 详细清理建议

### 说明文档
- `README_i18n_tools.md` - 本文档

## 工具概述

### 1. `check_i18n_usage.py` - 完整分析工具
功能最全面的i18n检查工具，提供详细的分析报告。

**特性：**
- 解析HOCON格式的i18n文件
- 自动忽略comments部分
- 扫描源码中的i18n键值使用情况
- 生成分类整理的详细报告
- 过滤误报和无关键值
- 提供清理建议

**使用方法：**
```bash
# 基本检查
python check_i18n_usage.py

# 生成详细报告到文件
python check_i18n_usage.py -o report.txt --detailed

# 自定义目录
python check_i18n_usage.py --i18n-dir path/to/i18n --source-dirs src1 src2
```

### 2. `quick_i18n_check.py` - 快速检查工具
轻量级的快速检查工具，适合日常使用。

**特性：**
- 快速扫描和分析
- 显示使用效率百分比
- 列出前10个未使用的键值
- 简洁的输出格式

**使用方法：**
```bash
python quick_i18n_check.py
```

### 3. `i18n_cleanup_helper.py` - 清理助手工具
帮助清理未使用的i18n键值。

**特性：**
- 生成详细的清理建议
- 显示每个未使用键值的行号
- 支持创建清理后的文件（干运行模式）
- 按类别组织未使用的键值

**使用方法：**
```bash
# 生成清理建议
python i18n_cleanup_helper.py -o cleanup_suggestions.txt

# 干运行模式查看会删除什么
python i18n_cleanup_helper.py --clean --dry-run

# 实际创建清理后的文件（谨慎使用）
python i18n_cleanup_helper.py --clean --force --clean-output-dir cleaned_i18n
```

## 检查结果总结

根据最新的检查结果：

### 📊 统计数据
- **总定义键值**: 146个
- **实际使用键值**: 85个
- **未使用键值**: 67个
- **使用效率**: 58.2%
- **缺失但被使用的键值**: 8个

### ⚠️ 主要发现

#### 未使用的键值类别：
1. **错误消息** (error.*) - 10个键值
2. **优化系统** (optimization.*) - 7个键值  
3. **警告消息** (warning.*) - 6个键值
4. **聊天系统** (chat.*) - 3个键值
5. **玩家相关** (player.*) - 3个键值
6. **注册系统** (registry.*) - 6个键值

#### 缺失的键值：
- `Bukkit.MaxHealth`
- `BukkitValues` 
- `ChunkBukkitValues`
- `dimension`
- `entries`
- `forgeCaps`
- `release-name.`

### 🔧 建议操作

#### 立即可以清理的键值：
1. **过时的实现者相关键值**: `Implementer.*`, `implementer.*`
2. **未使用的聊天功能**: `chat.illegal-characters`, `chat.message-too-long`
3. **调试相关**: `dist.logic-world-check`

#### 建议保留的键值：
1. **错误消息**: 虽然当前未使用，但对于错误处理很重要
2. **警告消息**: 可能在特定条件下使用
3. **优化系统消息**: 可能在开启特定优化功能时使用

#### 需要添加的键值：
为源码中使用但未定义的键值添加翻译。

## 使用建议

### 日常维护
1. 使用 `quick_i18n_check.py` 进行快速检查
2. 当使用效率低于70%时，考虑清理

### 深度分析
1. 使用 `check_i18n_usage.py --detailed` 生成完整报告
2. 使用 `i18n_cleanup_helper.py` 获取详细的清理建议

### 清理流程
1. 先运行干运行模式查看影响
2. 备份原始文件
3. 逐步清理，优先清理明确过时的键值
4. 测试确保功能正常

## 注意事项

1. **备份重要**: 清理前务必备份原始i18n文件
2. **测试验证**: 清理后要测试相关功能
3. **渐进式清理**: 不要一次性删除所有未使用的键值
4. **保留错误消息**: 错误和警告相关的键值建议保留
5. **多语言同步**: 清理时要保持所有语言文件的一致性

## 技术细节

### 支持的i18n使用模式
- `LOGGER.info("key")`
- `ArclightLocale.info("key")`
- `ArclightLocale.getInstance().get("key")`
- `logger.info("key")` (ArclightI18nLogger实例)

### 文件格式支持
- HOCON (.conf) 格式
- 自动处理嵌套键值结构
- 忽略comments部分

### 源码扫描范围
- Java (.java)
- Kotlin (.kt) 
- Scala (.scala)
- 自动跳过构建目录 (build, target, out)

---

这些工具帮助维护Luminara项目的i18n配置，确保翻译文件的整洁和高效使用。
